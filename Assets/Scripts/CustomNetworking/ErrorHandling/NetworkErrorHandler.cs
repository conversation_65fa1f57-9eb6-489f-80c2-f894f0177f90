using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using CustomNetworking.Core;
using CustomNetworking.Core.RPC;

namespace CustomNetworking.ErrorHandling
{
    /// <summary>
    /// 网络错误处理器 - 统一处理网络异常和恢复机制
    /// </summary>
    public class NetworkErrorHandler : MonoBehaviour
    {
        [Header("错误处理设置")]
        [SerializeField] private bool enableAutoRecovery = true;
        [SerializeField] private int maxRetryAttempts = 3;
        [SerializeField] private float retryDelay = 2f;
        [SerializeField] private float retryDelayMultiplier = 1.5f;
        [SerializeField] private float maxRetryDelay = 30f;

        [Header("连接监控")]
        [SerializeField] private float connectionCheckInterval = 5f;
        [SerializeField] private float connectionTimeout = 10f;
        [SerializeField] private int maxConsecutiveFailures = 3;

        [Header("错误分类")]
        [SerializeField] private bool logDetailedErrors = true;
        [SerializeField] private bool showUserFriendlyMessages = true;

        // 单例实例
        private static NetworkErrorHandler _instance;
        public static NetworkErrorHandler Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = FindFirstObjectByType<NetworkErrorHandler>(); // 使用FindFirstObjectByType替代已弃用的FindObjectOfType
                    if (_instance == null)
                    {
                        GameObject go = new GameObject("NetworkErrorHandler");
                        _instance = go.AddComponent<NetworkErrorHandler>();
                        DontDestroyOnLoad(go);
                    }
                }
                return _instance;
            }
        }

        // 错误状态
        private Dictionary<ErrorType, int> _errorCounts = new Dictionary<ErrorType, int>();
        private Dictionary<ErrorType, float> _lastErrorTimes = new Dictionary<ErrorType, float>();
        private Queue<NetworkError> _errorHistory = new Queue<NetworkError>();
        private const int MAX_ERROR_HISTORY = 50;

        // 恢复状态
        private bool _isRecovering;
        private Coroutine _recoveryCoroutine;
        private int _consecutiveFailures;
        private float _lastConnectionCheck;

        // 事件
        public event Action<NetworkError> OnErrorOccurred;
        public event Action<ErrorType> OnRecoveryStarted;
        public event Action<ErrorType> OnRecoveryCompleted;
        public event Action<ErrorType> OnRecoveryFailed;
        public event Action OnConnectionLost;
        public event Action OnConnectionRestored;

        private void Awake()
        {
            if (_instance == null)
            {
                _instance = this;
                DontDestroyOnLoad(gameObject);
                InitializeErrorHandler();
            }
            else if (_instance != this)
            {
                Destroy(gameObject);
            }
        }

        private void Update()
        {
            if (enableAutoRecovery)
            {
                MonitorConnection();
            }
        }

        /// <summary>
        /// 初始化错误处理器
        /// </summary>
        private void InitializeErrorHandler()
        {
            // 初始化错误计数
            foreach (ErrorType errorType in Enum.GetValues(typeof(ErrorType)))
            {
                _errorCounts[errorType] = 0;
                _lastErrorTimes[errorType] = 0f;
            }

            _lastConnectionCheck = Time.time;
        }

        /// <summary>
        /// 处理网络错误
        /// </summary>
        public void HandleError(ErrorType errorType, string message, Exception exception = null)
        {
            var networkError = new NetworkError
            {
                Type = errorType,
                Message = message,
                Exception = exception,
                Timestamp = Time.time,
                Severity = GetErrorSeverity(errorType)
            };

            // 记录错误
            RecordError(networkError);

            // 触发错误事件
            OnErrorOccurred?.Invoke(networkError);

            // 根据错误类型决定处理策略
            ProcessError(networkError);
        }

        /// <summary>
        /// 记录错误
        /// </summary>
        private void RecordError(NetworkError error)
        {
            // 更新错误统计
            _errorCounts[error.Type]++;
            _lastErrorTimes[error.Type] = error.Timestamp;

            // 添加到错误历史
            _errorHistory.Enqueue(error);
            if (_errorHistory.Count > MAX_ERROR_HISTORY)
            {
                _errorHistory.Dequeue();
            }

            // 记录日志
            if (logDetailedErrors)
            {
                LogError(error);
            }
        }

        /// <summary>
        /// 处理错误
        /// </summary>
        private void ProcessError(NetworkError error)
        {
            switch (error.Type)
            {
                case ErrorType.ConnectionLost:
                    HandleConnectionLost();
                    break;

                case ErrorType.ConnectionTimeout:
                    HandleConnectionTimeout();
                    break;

                case ErrorType.ServerUnreachable:
                    HandleServerUnreachable();
                    break;

                case ErrorType.AuthenticationFailed:
                    HandleAuthenticationFailed();
                    break;

                case ErrorType.RoomFull:
                    HandleRoomFull();
                    break;

                case ErrorType.RoomNotFound:
                    HandleRoomNotFound();
                    break;

                case ErrorType.NetworkDataCorrupted:
                    HandleDataCorruption();
                    break;

                case ErrorType.RpcFailed:
                    HandleRpcFailure();
                    break;

                case ErrorType.SynchronizationError:
                    HandleSynchronizationError();
                    break;

                case ErrorType.UnknownError:
                default:
                    HandleUnknownError(error);
                    break;
            }
        }

        /// <summary>
        /// 处理连接丢失
        /// </summary>
        private void HandleConnectionLost()
        {
            OnConnectionLost?.Invoke();

            if (enableAutoRecovery && !_isRecovering)
            {
                StartRecovery(ErrorType.ConnectionLost);
            }
        }

        /// <summary>
        /// 处理连接超时
        /// </summary>
        private void HandleConnectionTimeout()
        {
            _consecutiveFailures++;

            if (_consecutiveFailures >= maxConsecutiveFailures)
            {
                HandleConnectionLost();
            }
            else if (enableAutoRecovery)
            {
                StartRecovery(ErrorType.ConnectionTimeout);
            }
        }

        /// <summary>
        /// 处理服务器不可达
        /// </summary>
        private void HandleServerUnreachable()
        {
            if (enableAutoRecovery)
            {
                StartRecovery(ErrorType.ServerUnreachable);
            }
        }

        /// <summary>
        /// 处理认证失败
        /// </summary>
        private void HandleAuthenticationFailed()
        {
            // 认证失败通常不自动重试，需要用户重新登录
            ShowUserMessage("认证失败，请重新登录");
        }

        /// <summary>
        /// 处理房间已满
        /// </summary>
        private void HandleRoomFull()
        {
            ShowUserMessage("房间已满，请选择其他房间");
        }

        /// <summary>
        /// 处理房间未找到
        /// </summary>
        private void HandleRoomNotFound()
        {
            ShowUserMessage("房间不存在，请检查房间号");
        }

        /// <summary>
        /// 处理数据损坏
        /// </summary>
        private void HandleDataCorruption()
        {
            // 请求重新同步
            RequestResynchronization();
        }

        /// <summary>
        /// 处理RPC失败
        /// </summary>
        private void HandleRpcFailure()
        {
            // RPC失败可能需要重新发送
            if (enableAutoRecovery)
            {
                StartRecovery(ErrorType.RpcFailed);
            }
        }

        /// <summary>
        /// 处理同步错误
        /// </summary>
        private void HandleSynchronizationError()
        {
            RequestResynchronization();
        }

        /// <summary>
        /// 处理未知错误
        /// </summary>
        private void HandleUnknownError(NetworkError error)
        {
            UnityEngine.Debug.LogWarning($"Unknown network error: {error.Message}");

            if (enableAutoRecovery)
            {
                StartRecovery(ErrorType.UnknownError);
            }
        }

        /// <summary>
        /// 开始恢复过程
        /// </summary>
        private void StartRecovery(ErrorType errorType)
        {
            if (_isRecovering)
            {
                return;
            }

            _isRecovering = true;
            OnRecoveryStarted?.Invoke(errorType);

            _recoveryCoroutine = StartCoroutine(RecoveryProcess(errorType));
        }

        /// <summary>
        /// 恢复过程协程
        /// </summary>
        private IEnumerator RecoveryProcess(ErrorType errorType)
        {
            int attempts = 0;
            float currentDelay = retryDelay;

            while (attempts < maxRetryAttempts)
            {
                attempts++;

                UnityEngine.Debug.Log($"尝试恢复网络连接 (第 {attempts}/{maxRetryAttempts} 次)");

                // 等待重试延迟
                yield return new WaitForSeconds(currentDelay);

                // 尝试恢复
                bool success = false;

                // 使用协程方式调用异步方法
                var recoveryTask = AttemptRecovery(errorType);
                yield return new WaitUntil(() => recoveryTask.IsCompleted);

                try
                {
                    success = recoveryTask.Result;
                }
                catch (Exception ex)
                {
                    UnityEngine.Debug.LogError($"恢复尝试失败: {ex.Message}");
                }

                if (success)
                {
                    // 恢复成功
                    _isRecovering = false;
                    _consecutiveFailures = 0;
                    OnRecoveryCompleted?.Invoke(errorType);
                    OnConnectionRestored?.Invoke();

                    UnityEngine.Debug.Log("网络连接恢复成功");
                    yield break;
                }

                // 增加重试延迟
                currentDelay = Mathf.Min(currentDelay * retryDelayMultiplier, maxRetryDelay);
            }

            // 所有重试都失败了
            _isRecovering = false;
            OnRecoveryFailed?.Invoke(errorType);

            UnityEngine.Debug.LogError($"网络恢复失败，已尝试 {maxRetryAttempts} 次");
            ShowUserMessage("网络连接恢复失败，请检查网络设置");
        }

        /// <summary>
        /// 尝试恢复连接
        /// </summary>
        private async System.Threading.Tasks.Task<bool> AttemptRecovery(ErrorType errorType)
        {
            var networkManager = GetNetworkManager();
            if (networkManager == null)
            {
                return false;
            }

            try
            {
                switch (errorType)
                {
                    case ErrorType.ConnectionLost:
                    case ErrorType.ConnectionTimeout:
                    case ErrorType.ServerUnreachable:
                        // 尝试重新连接
                        await networkManager.ReconnectAsync();
                        break;

                    case ErrorType.RpcFailed:
                        // 重新初始化RPC系统
                        RpcManager.Instance.Cleanup();
                        var runner = FindFirstObjectByType<NetworkRunner>(); // 使用FindFirstObjectByType替代已弃用的FindObjectOfType
                        if (runner != null)
                        {
                            RpcManager.Instance.Initialize(runner);
                        }
                        break;

                    case ErrorType.SynchronizationError:
                        // 请求完全重新同步
                        await RequestFullResynchronization();
                        break;

                    default:
                        return false;
                }

                // 验证连接是否恢复
                return await VerifyConnection();
            }
            catch (Exception ex)
            {
                UnityEngine.Debug.LogError($"恢复过程中发生异常: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 验证连接状态
        /// </summary>
        private async System.Threading.Tasks.Task<bool> VerifyConnection()
        {
            var networkManager = GetNetworkManager();
            if (networkManager == null || !networkManager.IsConnected)
            {
                return false;
            }

            // 发送心跳包验证连接
            try
            {
                await networkManager.SendHeartbeat();
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 监控连接状态
        /// </summary>
        private void MonitorConnection()
        {
            float currentTime = Time.time;

            if (currentTime - _lastConnectionCheck >= connectionCheckInterval)
            {
                _lastConnectionCheck = currentTime;

                var networkManager = GetNetworkManager();
                if (networkManager != null && networkManager.IsConnected)
                {
                    // 检查连接质量
                    CheckConnectionQuality();
                }
            }
        }

        /// <summary>
        /// 检查连接质量
        /// </summary>
        private void CheckConnectionQuality()
        {
            // 使用connectionTimeout字段进行连接超时检查
            var networkManager = GetNetworkManager();
            if (networkManager != null)
            {
                // 简化实现，检查连接状态
                if (!networkManager.IsConnected)
                {
                    LogError($"Connection timeout detected after {connectionTimeout}s");

                    // 触发连接超时处理
                    HandleConnectionTimeout();
                }
            }
        }

        /// <summary>
        /// 请求重新同步
        /// </summary>
        private void RequestResynchronization()
        {
            var networkManager = GetNetworkManager();
            if (networkManager != null)
            {
                networkManager.RequestResync();
            }
        }

        /// <summary>
        /// 请求完全重新同步
        /// </summary>
        private async System.Threading.Tasks.Task RequestFullResynchronization()
        {
            var networkManager = GetNetworkManager();
            if (networkManager != null)
            {
                await networkManager.RequestFullResync();
            }
        }

        /// <summary>
        /// 获取错误严重程度
        /// </summary>
        private ErrorSeverity GetErrorSeverity(ErrorType errorType)
        {
            switch (errorType)
            {
                case ErrorType.ConnectionLost:
                case ErrorType.ServerUnreachable:
                case ErrorType.AuthenticationFailed:
                    return ErrorSeverity.Critical;

                case ErrorType.ConnectionTimeout:
                case ErrorType.SynchronizationError:
                case ErrorType.NetworkDataCorrupted:
                    return ErrorSeverity.High;

                case ErrorType.RpcFailed:
                case ErrorType.RoomFull:
                case ErrorType.RoomNotFound:
                    return ErrorSeverity.Medium;

                default:
                    return ErrorSeverity.Low;
            }
        }

        /// <summary>
        /// 记录错误日志
        /// </summary>
        private void LogError(NetworkError error)
        {
            string logMessage = $"[NetworkError] {error.Type}: {error.Message}";

            switch (error.Severity)
            {
                case ErrorSeverity.Critical:
                    UnityEngine.Debug.LogError(logMessage);
                    break;
                case ErrorSeverity.High:
                    UnityEngine.Debug.LogWarning(logMessage);
                    break;
                default:
                    UnityEngine.Debug.Log(logMessage);
                    break;
            }

            if (error.Exception != null)
            {
                UnityEngine.Debug.LogException(error.Exception);
            }
        }

        /// <summary>
        /// 显示用户友好的错误消息
        /// </summary>
        private void ShowUserMessage(string message)
        {
            if (showUserFriendlyMessages)
            {
                // 这里可以集成UI系统显示错误消息
                UnityEngine.Debug.Log($"[用户消息] {message}");
            }
        }

        /// <summary>
        /// 获取错误统计
        /// </summary>
        public Dictionary<ErrorType, int> GetErrorStatistics()
        {
            return new Dictionary<ErrorType, int>(_errorCounts);
        }

        #region 测试辅助方法

        /// <summary>
        /// 触发错误事件 (仅用于测试)
        /// </summary>
        public void TriggerError(NetworkError error)
        {
            OnErrorOccurred?.Invoke(error);
        }

        /// <summary>
        /// 触发连接丢失事件 (仅用于测试)
        /// </summary>
        public void TriggerConnectionLost()
        {
            OnConnectionLost?.Invoke();
        }

        /// <summary>
        /// 触发连接恢复事件 (仅用于测试)
        /// </summary>
        public void TriggerConnectionRestored()
        {
            OnConnectionRestored?.Invoke();
        }

        #endregion

        /// <summary>
        /// 获取网络管理器实例
        /// </summary>
        private INetworkManager GetNetworkManager()
        {
            // 查找实现了INetworkManager接口的组件
            var allComponents = FindObjectsByType<MonoBehaviour>(FindObjectsSortMode.None); // 使用FindObjectsByType替代已弃用的FindObjectsOfType
            foreach (var component in allComponents)
            {
                if (component is INetworkManager networkManager)
                {
                    return networkManager;
                }
            }
            return null;
        }

        /// <summary>
        /// 获取错误历史
        /// </summary>
        public NetworkError[] GetErrorHistory()
        {
            return _errorHistory.ToArray();
        }

        /// <summary>
        /// 清除错误历史
        /// </summary>
        public void ClearErrorHistory()
        {
            _errorHistory.Clear();

            foreach (var key in _errorCounts.Keys.ToArray())
            {
                _errorCounts[key] = 0;
            }
        }

        /// <summary>
        /// 设置自动恢复
        /// </summary>
        public void SetAutoRecovery(bool enabled)
        {
            enableAutoRecovery = enabled;
        }

        private void OnDestroy()
        {
            if (_recoveryCoroutine != null)
            {
                StopCoroutine(_recoveryCoroutine);
            }
        }
    }
}
